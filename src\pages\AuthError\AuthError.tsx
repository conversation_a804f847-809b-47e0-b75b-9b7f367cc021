import React, { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { AuthContainer } from '../../components/organisms';
import { Typography } from '../../components/atoms';
import { AlertCircle, RefreshCw, ArrowLeft } from 'lucide-react';

const AuthError: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const [error, setError] = useState<string>('');
  const [details, setDetails] = useState<string>('');

  useEffect(() => {
    const errorParam = searchParams.get('error') || 'unknown_error';
    const detailsParam = searchParams.get('details') || '';
    
    setError(errorParam);
    setDetails(detailsParam);
    
    console.log('🚨 AuthError: OAuth error occurred', {
      error: errorParam,
      details: detailsParam,
      url: window.location.href
    });
  }, [searchParams]);

  const getErrorMessage = (errorCode: string) => {
    switch (errorCode) {
      case 'callback_failed':
        return {
          title: 'Authentication Failed',
          message: 'There was an issue processing your login. This usually happens due to a backend configuration problem.',
          suggestion: 'Please try again or contact support if the issue persists.'
        };
      case 'access_denied':
        return {
          title: 'Access Denied',
          message: 'You denied access to your account or cancelled the login process.',
          suggestion: 'Click "Try Again" to restart the authentication process.'
        };
      case 'invalid_request':
        return {
          title: 'Invalid Request',
          message: 'The authentication request was invalid or malformed.',
          suggestion: 'Please try logging in again.'
        };
      default:
        return {
          title: 'Authentication Error',
          message: 'An unexpected error occurred during authentication.',
          suggestion: 'Please try again or contact support.'
        };
    }
  };

  const errorInfo = getErrorMessage(error);

  const handleTryAgain = () => {
    console.log('🔄 AuthError: User trying again');
    navigate('/login', { replace: true });
  };

  const handleGoHome = () => {
    console.log('🏠 AuthError: User going home');
    navigate('/', { replace: true });
  };

  return (
    <AuthContainer>
      <div className="text-center space-y-6">
        {/* Error Icon */}
        <div className="flex justify-center">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center">
            <AlertCircle className="w-8 h-8 text-red-600" />
          </div>
        </div>

        {/* Error Title */}
        <div>
          <Typography variant="h2" color="gray" className="text-2xl font-bold text-gray-900 mb-2">
            {errorInfo.title}
          </Typography>
          <Typography variant="body1" color="gray" className="text-gray-600">
            {errorInfo.message}
          </Typography>
        </div>

        {/* Error Details (for debugging) */}
        {details && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 text-left">
            <Typography variant="body2" color="gray" className="font-medium text-red-800 mb-2">
              Technical Details:
            </Typography>
            <Typography variant="caption" color="gray" className="text-red-700 text-sm font-mono break-all">
              {details}
            </Typography>
          </div>
        )}

        {/* Suggestion */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <Typography variant="body2" color="gray" className="text-blue-800">
            💡 {errorInfo.suggestion}
          </Typography>
        </div>

        {/* Action Buttons */}
        <div className="space-y-3">
          <button
            onClick={handleTryAgain}
            className="w-full flex items-center justify-center px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <RefreshCw className="w-5 h-5 mr-2" />
            Try Again
          </button>
          
          <button
            onClick={handleGoHome}
            className="w-full flex items-center justify-center px-4 py-3 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
          >
            <ArrowLeft className="w-5 h-5 mr-2" />
            Go to Homepage
          </button>
        </div>

        {/* Debug Information */}
        {process.env.NODE_ENV === 'development' && (
          <div className="mt-8 p-4 bg-gray-100 rounded-lg text-left">
            <Typography variant="body2" color="gray" className="font-medium text-gray-800 mb-2">
              Debug Information:
            </Typography>
            <pre className="text-xs text-gray-600 overflow-x-auto">
              {JSON.stringify({
                error,
                details,
                url: window.location.href,
                timestamp: new Date().toISOString()
              }, null, 2)}
            </pre>
          </div>
        )}

        {/* Common Issues */}
        <div className="text-left bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <Typography variant="body2" color="gray" className="font-medium text-yellow-800 mb-2">
            Common Issues:
          </Typography>
          <ul className="text-sm text-yellow-700 space-y-1">
            <li>• Backend server might be down or misconfigured</li>
            <li>• Google OAuth credentials might be invalid</li>
            <li>• Database connection issues</li>
            <li>• CORS or redirect URI configuration problems</li>
          </ul>
        </div>
      </div>
    </AuthContainer>
  );
};

export default AuthError;
