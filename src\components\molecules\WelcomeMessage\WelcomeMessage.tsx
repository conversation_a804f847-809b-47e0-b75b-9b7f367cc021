import React, { useEffect, useState } from 'react';
import { Typography } from '../../atoms';
import { loadUserData } from '../../../utils/userDataMigration';
import { cn } from '../../../utils/cn';

interface WelcomeMessageProps {
  className?: string;
}

const WelcomeMessage: React.FC<WelcomeMessageProps> = ({ className = '' }) => {
  const [user, setUser] = useState<any>(null);
  const [greeting, setGreeting] = useState('');

  useEffect(() => {
    const userData = loadUserData();
    if (userData) {
      setUser(userData);
      console.log('✅ WelcomeMessage: Loaded user data:', userData);
    }

    // Set greeting based on time of day
    const hour = new Date().getHours();
    if (hour < 12) {
      setGreeting('Good morning');
    } else if (hour < 17) {
      setGreeting('Good afternoon');
    } else {
      setGreeting('Good evening');
    }
  }, []);

  const getFirstName = (fullName: string) => {
    return fullName?.split(' ')[0] || 'User';
  };

  if (!user) {
    return (
      <div className={cn('animate-pulse', className)}>
        <div className="h-8 bg-gray-300 rounded w-64 mb-2"></div>
        <div className="h-4 bg-gray-300 rounded w-48"></div>
      </div>
    );
  }

  return (
    <div className={cn('', className)}>
      <Typography variant="h2" color="gray" className="text-2xl font-bold mb-2">
        {greeting}, {getFirstName(user.name)}! 👋
      </Typography>
      <Typography variant="body1" color="gray" className="text-gray-600">
        Welcome back to your ScapeGIS workspace. 
        {user.provider && (
          <span className="ml-1 text-sm opacity-75">
            (Signed in via {user.provider})
          </span>
        )}
      </Typography>
    </div>
  );
};

export default WelcomeMessage;
