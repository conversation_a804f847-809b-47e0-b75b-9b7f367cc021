import React, { useState } from 'react';
import { authApi } from '../../../services/api';

interface OAuthDebugProps {
  className?: string;
}

const OAuthDebug: React.FC<OAuthDebugProps> = ({ className = '' }) => {
  const [debugInfo, setDebugInfo] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const testOAuthFlow = async () => {
    setLoading(true);
    setError(null);
    setDebugInfo(null);

    try {
      // Get current tokens and user data
      const accessToken = localStorage.getItem('access_token');
      const refreshToken = localStorage.getItem('refresh_token');
      const userData = localStorage.getItem('user_data');

      const info: any = {
        timestamp: new Date().toISOString(),
        localStorage: {
          hasAccessToken: !!accessToken,
          hasRefreshToken: !!refreshToken,
          hasUserData: !!userData,
          accessTokenPreview: accessToken ? `${accessToken.substring(0, 20)}...` : null,
          refreshTokenPreview: refreshToken ? `${refreshToken.substring(0, 20)}...` : null,
          userData: userData ? JSON.parse(userData) : null
        }
      };

      // Test API endpoints
      if (accessToken) {
        try {
          console.log('🧪 Testing profile endpoint with token...');
          const profileResponse = await authApi.getProfile();
          info.profileTest = {
            success: true,
            data: profileResponse.data,
            status: profileResponse.status
          };
          console.log('✅ Profile endpoint test successful:', profileResponse.data);
        } catch (profileError: any) {
          console.error('❌ Profile endpoint test failed:', profileError);
          info.profileTest = {
            success: false,
            error: profileError.message,
            status: profileError.response?.status,
            data: profileError.response?.data
          };
        }
      } else {
        info.profileTest = {
          success: false,
          error: 'No access token available'
        };
      }

      setDebugInfo(info);
    } catch (err: any) {
      setError(err.message);
      console.error('OAuth debug test failed:', err);
    } finally {
      setLoading(false);
    }
  };

  const clearTokens = () => {
    localStorage.removeItem('access_token');
    localStorage.removeItem('refresh_token');
    localStorage.removeItem('user_data');
    setDebugInfo(null);
    setError(null);
    console.log('🧹 Cleared all OAuth tokens and user data');
  };

  const simulateOAuthCallback = () => {
    const testUrl = `${window.location.origin}/auth/callback?oauth_success=true&access_token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************.test_signature&refresh_token=test_refresh_token_67890&user_id=user_123&email=test%40example.com&name=Test%20User&avatar_url=https%3A//example.com/avatar.jpg&provider=google&message=login_successful`;
    window.location.href = testUrl;
  };

  const startRealOAuth = () => {
    window.location.href = `${process.env.REACT_APP_API_URL}/api/v1/auth/oauth/google`;
  };

  return (
    <div className={`bg-white p-6 rounded-lg shadow-md ${className}`}>
      <h3 className="text-lg font-semibold mb-4">🔧 OAuth Debug Panel</h3>
      
      <div className="space-y-3 mb-6">
        <button
          onClick={testOAuthFlow}
          disabled={loading}
          className="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
        >
          {loading ? 'Testing...' : '🧪 Test Current OAuth State'}
        </button>
        
        <button
          onClick={simulateOAuthCallback}
          className="w-full px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
        >
          🎭 Simulate OAuth Callback
        </button>
        
        <button
          onClick={startRealOAuth}
          className="w-full px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700"
        >
          🚀 Start Real Google OAuth
        </button>
        
        <button
          onClick={clearTokens}
          className="w-full px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
        >
          🧹 Clear All Tokens
        </button>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          <strong>Error:</strong> {error}
        </div>
      )}

      {debugInfo && (
        <div className="bg-gray-100 p-4 rounded-md">
          <h4 className="font-semibold mb-2">Debug Information:</h4>
          <pre className="text-xs overflow-x-auto whitespace-pre-wrap">
            {JSON.stringify(debugInfo, null, 2)}
          </pre>
        </div>
      )}
    </div>
  );
};

export default OAuthDebug;
