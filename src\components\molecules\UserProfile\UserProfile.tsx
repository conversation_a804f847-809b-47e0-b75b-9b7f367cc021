import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Avatar, Typography } from '../../atoms';
import { loadUserData, clearUserData } from '../../../utils/userDataMigration';
import { useAuthStore } from '../../../stores/authStore';
import { cn } from '../../../utils/cn';

interface UserProfileProps {
  className?: string;
  showDropdown?: boolean;
}

const UserProfile: React.FC<UserProfileProps> = ({ 
  className = '',
  showDropdown = true 
}) => {
  const navigate = useNavigate();
  const { logout } = useAuthStore();
  const [user, setUser] = useState<any>(null);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);

  useEffect(() => {
    const userData = loadUserData();
    if (userData) {
      setUser(userData);
      console.log('✅ UserProfile: Loaded user data:', userData);
    }
  }, []);

  const handleLogout = async () => {
    try {
      console.log('🔄 UserProfile: Logging out user');
      await logout();
      clearUserData();
      navigate('/login', { replace: true });
    } catch (error) {
      console.error('❌ UserProfile: Logout error:', error);
      // Force logout even if API call fails
      clearUserData();
      navigate('/login', { replace: true });
    }
  };

  const toggleDropdown = () => {
    setIsDropdownOpen(!isDropdownOpen);
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;
      if (!target.closest('.user-profile-dropdown')) {
        setIsDropdownOpen(false);
      }
    };

    if (isDropdownOpen) {
      document.addEventListener('click', handleClickOutside);
      return () => document.removeEventListener('click', handleClickOutside);
    }
  }, [isDropdownOpen]);

  if (!user) {
    return (
      <div className={cn('flex items-center space-x-2', className)}>
        <div className="animate-pulse">
          <div className="w-8 h-8 bg-gray-300 rounded-full"></div>
        </div>
        <div className="hidden md:block animate-pulse">
          <div className="w-20 h-4 bg-gray-300 rounded"></div>
        </div>
      </div>
    );
  }

  return (
    <div className={cn('relative user-profile-dropdown', className)}>
      <button
        onClick={showDropdown ? toggleDropdown : undefined}
        className={cn(
          'flex items-center space-x-3 p-2 rounded-lg transition-colors',
          showDropdown ? 'hover:bg-gray-100' : '',
          isDropdownOpen ? 'bg-gray-100' : ''
        )}
      >
        <Avatar 
          name={user.name || 'User'} 
          src={user.avatar || user.avatar_url} 
          size="sm" 
        />
        <div className="hidden md:block text-left">
          <Typography variant="body2" color="gray" className="font-medium">
            {user.name || 'User'}
          </Typography>
          <Typography variant="caption" color="gray" className="text-xs opacity-75">
            {user.email || '<EMAIL>'}
          </Typography>
        </div>
        {showDropdown && (
          <svg 
            className={cn(
              'w-4 h-4 text-gray-400 transition-transform hidden md:block',
              isDropdownOpen ? 'rotate-180' : ''
            )} 
            fill="none" 
            stroke="currentColor" 
            viewBox="0 0 24 24"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </svg>
        )}
      </button>

      {/* Dropdown Menu */}
      {showDropdown && isDropdownOpen && (
        <div className="absolute right-0 top-full mt-2 w-64 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50">
          {/* User Info */}
          <div className="px-4 py-3 border-b border-gray-100">
            <div className="flex items-center space-x-3">
              <Avatar 
                name={user.name || 'User'} 
                src={user.avatar || user.avatar_url} 
                size="md" 
              />
              <div className="flex-1 min-w-0">
                <Typography variant="body2" color="gray" className="font-medium">
                  {user.name || 'User'}
                </Typography>
                <Typography variant="caption" color="gray" className="text-xs opacity-75 truncate">
                  {user.email || '<EMAIL>'}
                </Typography>
                {user.provider && (
                  <Typography variant="caption" color="gray" className="text-xs opacity-60">
                    via {user.provider}
                  </Typography>
                )}
              </div>
            </div>
          </div>

          {/* Menu Items */}
          <div className="py-1">
            <button
              onClick={() => {
                setIsDropdownOpen(false);
                // Navigate to profile settings
                console.log('Navigate to profile settings');
              }}
              className="w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100 flex items-center space-x-2"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
              </svg>
              <span>Profile Settings</span>
            </button>

            <button
              onClick={() => {
                setIsDropdownOpen(false);
                // Navigate to account settings
                console.log('Navigate to account settings');
              }}
              className="w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100 flex items-center space-x-2"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
              </svg>
              <span>Account Settings</span>
            </button>

            <div className="border-t border-gray-100 my-1"></div>

            <button
              onClick={handleLogout}
              className="w-full px-4 py-2 text-left text-sm text-red-600 hover:bg-red-50 flex items-center space-x-2"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
              </svg>
              <span>Sign Out</span>
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default UserProfile;
