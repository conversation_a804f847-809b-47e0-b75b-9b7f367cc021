/**
 * Utility functions for handling user data migration and compatibility
 */

export interface UserData {
  id: string;
  email: string;
  name: string;
  avatar_url?: string;
  avatar?: string; // For compatibility
  provider?: string;
  workspace?: string;
}

/**
 * Migrates and normalizes user data to ensure compatibility with all components
 */
export const migrateUserData = (userData: any): UserData => {
  if (!userData) {
    return {
      id: 'anonymous',
      email: '<EMAIL>',
      name: 'Anonymous User',
      workspace: 'Personal Workspace'
    };
  }

  // Ensure all required fields are present
  const migrated: UserData = {
    id: userData.id || userData.user_id || 'anonymous',
    email: userData.email || '<EMAIL>',
    name: userData.name || userData.full_name || 'User',
    avatar_url: userData.avatar_url || userData.avatar || '',
    avatar: userData.avatar || userData.avatar_url || '', // For compatibility
    provider: userData.provider || 'unknown',
    workspace: userData.workspace || 'Personal Workspace'
  };

  return migrated;
};

/**
 * Loads user data from localStorage and applies migration
 */
export const loadUserData = (): UserData | null => {
  try {
    const userData = localStorage.getItem('user_data');
    if (!userData) {
      return null;
    }

    const parsed = JSON.parse(userData);
    const migrated = migrateUserData(parsed);
    
    // Save the migrated data back to localStorage
    localStorage.setItem('user_data', JSON.stringify(migrated));
    
    return migrated;
  } catch (error) {
    console.error('Error loading user data:', error);
    return null;
  }
};

/**
 * Saves user data to localStorage with proper structure
 */
export const saveUserData = (userData: Partial<UserData>): void => {
  try {
    const migrated = migrateUserData(userData);
    localStorage.setItem('user_data', JSON.stringify(migrated));
  } catch (error) {
    console.error('Error saving user data:', error);
  }
};

/**
 * Clears all user authentication data
 */
export const clearUserData = (): void => {
  localStorage.removeItem('access_token');
  localStorage.removeItem('refresh_token');
  localStorage.removeItem('user_data');
};
