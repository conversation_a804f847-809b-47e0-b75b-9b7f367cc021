import React, { useEffect, useState } from 'react';
import { Typography } from '../../atoms';
import { loadUserData } from '../../../utils/userDataMigration';
import { cn } from '../../../utils/cn';

interface UserStatsProps {
  className?: string;
}

interface UserStatsData {
  projectsCount: number;
  lastLoginDate: string;
  accountCreated: string;
  storageUsed: string;
}

const UserStats: React.FC<UserStatsProps> = ({ className = '' }) => {
  const [user, setUser] = useState<any>(null);
  const [stats, setStats] = useState<UserStatsData | null>(null);

  useEffect(() => {
    const userData = loadUserData();
    if (userData) {
      setUser(userData);
      
      // Mock stats data - in real app, this would come from API
      const mockStats: UserStatsData = {
        projectsCount: 3,
        lastLoginDate: new Date().toLocaleDateString(),
        accountCreated: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toLocaleDateString(), // 30 days ago
        storageUsed: '2.4 MB'
      };
      
      setStats(mockStats);
      console.log('✅ UserStats: Loaded user stats:', mockStats);
    }
  }, []);

  if (!user || !stats) {
    return (
      <div className={cn('animate-pulse', className)}>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {[1, 2, 3, 4].map((i) => (
            <div key={i} className="bg-gray-100 p-4 rounded-lg">
              <div className="h-4 bg-gray-300 rounded w-16 mb-2"></div>
              <div className="h-6 bg-gray-300 rounded w-12"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  const statItems = [
    {
      label: 'Projects',
      value: stats.projectsCount.toString(),
      icon: (
        <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
        </svg>
      )
    },
    {
      label: 'Last Login',
      value: stats.lastLoginDate,
      icon: (
        <svg className="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      )
    },
    {
      label: 'Member Since',
      value: stats.accountCreated,
      icon: (
        <svg className="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
        </svg>
      )
    },
    {
      label: 'Storage Used',
      value: stats.storageUsed,
      icon: (
        <svg className="w-5 h-5 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4" />
        </svg>
      )
    }
  ];

  return (
    <div className={cn('', className)}>
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        {statItems.map((item, index) => (
          <div
            key={index}
            className="bg-white p-4 rounded-lg border border-gray-200 hover:shadow-md transition-shadow"
          >
            <div className="flex items-center space-x-3">
              <div className="flex-shrink-0">
                {item.icon}
              </div>
              <div className="flex-1 min-w-0">
                <Typography variant="caption" color="gray" className="text-xs text-gray-500 uppercase tracking-wide">
                  {item.label}
                </Typography>
                <Typography variant="body2" color="gray" className="font-semibold text-gray-900 truncate">
                  {item.value}
                </Typography>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default UserStats;
